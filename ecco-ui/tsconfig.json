{
  "compilerOptions": {
    "composite": true,
    "target": "ES6",
    "lib": [
      "dom",
      "dom.iterable",
      "es2017.object",
      "es2015" // I had to also add "--lib dom,es2015" etc to my IDEA compiler options
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "node",
//    "noEmit": true,
    "jsx": "react"
  },
  "include": [
    "xx"
  ],
  "references": [
    {"path": "./ecco-admin/tsconfig.json"},
    {"path": "./ecco-dto/tsconfig.json"},
    {"path": "./ecco-calendar/tsconfig.json"},
    {"path": "./ecco-commands/tsconfig.json"},
    {"path": "./ecco-cubejs/tsconfig.json"},
    {"path": "./ecco-finance/tsconfig.json"},
    {"path": "./ecco-incidents/tsconfig.json"},
    {"path": "./ecco-managedvoids/tsconfig.json"},
    {"path": "./ecco-repairs/tsconfig.json"},
    {"path": "./ecco-mui/tsconfig.json"},
    {"path": "./ecco-mui-controls/tsconfig.json"},
    {"path": "./ecco-evidence/tsconfig.json"},
    {"path": "./ecco-components/tsconfig.json"},
    {"path": "./ecco-math/tsconfig.json"},
    {"path": "./ecco-offline-data/tsconfig.json"},
    {"path": "./ecco-rota/tsconfig.json"}
  ]
}
