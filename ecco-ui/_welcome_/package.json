{"name": "ecco-welcome-menu", "version": "0.1.0", "private": true, "homepage": "Ignored - Use .env PUBLIC_URL for build target URL", "dependencies": {"@eccosolutions/ecco-common": "1.8.4", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui": "0.0.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^7.1.2", "@types/jest": "^29.5.2", "@types/node": "^14.18.12", "@types/react": "^16.9.19", "@types/react-dom": "^16.9.5", "@types/react-router": "^5.1.5", "@types/react-router-dom": "^5.1.3", "application-properties": "0.0.0", "ecco-components": "0.0.0", "ecco-cubejs": "^0.0.0", "ecco-dto": "0.0.0", "ecco-finance": "0.0.0", "ecco-incidents": "^0.0.0", "ecco-managedvoids": "^0.0.0", "ecco-repairs": "^0.0.0", "ecco-offline": "0.0.0", "ecco-rota": "0.0.0", "font-awesome": "^4.3.0", "jquery": "3.6.0", "jquery-migrate": "3.3.2", "lazy": "^1.0.11", "prop-types": "^15.5.8", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0"}, "devDependencies": {"cross-env": "7.0.3", "customize-cra": "^1.0.0-alpha.0", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "react-app-rewired": "^2.1.5", "react-app-rewired-esbuild": "^0.0.11", "react-scripts": "4.0.3", "tslint": "5.20.1", "tslint-react": "4.1.0", "typescript": "5.1.5", "webpack-bundle-analyzer": "^4.4.0"}, "scripts": {"analyze": "cross-env BUNDLE_VISUALIZE=1 react-app-rewired start --no-cache", "start": "cross-env PUBLIC_URL=http://localhost:3000 react-app-rewired start --no-cache", "cra:build": "node_modules/.bin/react-app-rewired build", "clean": "node_modules/.bin/tsc --build --clean tsconfig.json", "test": "echo 'was react-app-rewired test but broken'", "eject": "node_modules/.bin/react-scripts eject", "lint-fix": "node_modules/.bin/tslint --project . --fix", "lint": "node_modules/.bin/tslint --project .", "build": "echo 'not for production so no yarn build'", "check": "node_modules/.bin/tsc --build tsconfig-composite.json --verbose && echo 'Just did quick tsc check for now'", "emit": "yarn cra:build"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}