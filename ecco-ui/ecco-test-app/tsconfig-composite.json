{"compilerOptions": {"composite": true, "target": "ES6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": false, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "rootDir": ".", "outDir": "./build-tsc", "jsx": "preserve"}, "include": ["src"], "exclude": ["src/**/*.js"], "references": [{"path": "../ecco-admin/tsconfig.json"}, {"path": "../ecco-calendar/tsconfig.json"}, {"path": "../ecco-commands/tsconfig.json"}, {"path": "../ecco-components/tsconfig.json"}, {"path": "../ecco-cubejs/tsconfig.json"}, {"path": "../ecco-dto/tsconfig.json"}, {"path": "../ecco-evidence/tsconfig.json"}, {"path": "../ecco-finance/tsconfig.json"}, {"path": "../ecco-forms/tsconfig.json"}, {"path": "../ecco-incidents/tsconfig.json"}, {"path": "../ecco-managedvoids/tsconfig.json"}, {"path": "../ecco-repairs/tsconfig.json"}, {"path": "../ecco-mui/tsconfig.json"}, {"path": "../ecco-rota/tsconfig.json"}]}