package com.ecco.offline;

import com.ecco.web.EccoMediaTypes;
import org.springframework.http.MediaType;

public class RarelyChangingStaticFileResourceProvider extends BaseStaticFileResourceProvider {
    public RarelyChangingStaticFileResourceProvider() {

        // Should be able to get this from EccoEnvironment, but doesn't autowire - not in ecco-servlet context
        boolean dev = "dev".equals(System.getProperty("env"));

        addCss("bootstrap/css/bootstrap-modal-bs3patch.css");
        addCss("bootstrap/css/bootstrap-modal.css");
        addCss("bootstrap/css/bootstrap-theme.min.css");
        addCss("bootstrap/css/bootstrap.min.css");
        addCachedResource("bootstrap/fonts/glyphicons-halflings-regular.woff2", EccoMediaTypes.FONT_WOFF2);

        addCss("font-awesome/css/font-awesome.min.css");
        addCachedResource("font-awesome/fonts/fontawesome-webfont.woff2", EccoMediaTypes.FONT_WOFF2);

        addCss("css/jqueryui/jqueryui.css");

        addCss("css/qunit/qunit.css");
        addCss("css/select2/select2.min.css");
        addCss("css/typeahead.css");
        addCss("css/hopscotch.min.css");

        addPng("img/sprite-green.png");
        addPng("img/sprite-orange.png");

        addCss("scripts/jquery.jqplot.min.css");

        addScript("common/tabEvents.js");
        addScript("common/events.js");
        addScript("common/RequestPath.js");
        addLib("common/require-boot.js");

        // eg ecco-offline/target/classes/com/ecco/offline/staticFiles/build/dist/ecco-finance.js
        addDistAndOptionallyDevScript("ecco-admin.js", dev); // Served via /ui/build/ for full website
        addDistAndOptionallyDevScript("ecco-calendar.js", dev);
        addDistAndOptionallyDevScript("ecco-commands.js", dev);
        addDistAndOptionallyDevScript("ecco-common.js", dev);
        addDistAndOptionallyDevScript("ecco-components.js", dev);
        addDistAndOptionallyDevScript("ecco-cubejs.js", dev);
        addDistAndOptionallyDevScript("ecco-crypto.js", dev);
        addDistAndOptionallyDevScript("ecco-dto.js", dev);
        addDistAndOptionallyDevScript("ecco-evidence.js", dev);
        addDistAndOptionallyDevScript("ecco-finance.js", dev);
        addScript("dist/ecco-forms.js");        // Don't do this until debug/ is not 26Mb!    addDistAndOptionallyDevScript("ecco-forms.js");
        addDistAndOptionallyDevScript("ecco-incidents.js", dev);
        addDistAndOptionallyDevScript("ecco-managedvoids.js", dev);
        addDistAndOptionallyDevScript("ecco-repairs.js", dev);
        addDistAndOptionallyDevScript("ecco-math.js", dev);
        addDistAndOptionallyDevScript("ecco-mui.js", dev);
        addDistAndOptionallyDevScript("ecco-mui-controls.js", dev);
        addDistAndOptionallyDevScript("ecco-offline-data.js", dev);
        addDistAndOptionallyDevScript("ecco-rota.js", dev);

        addLib("draw/raphael.js");

        addLib("lib/bloodhound.min.js");
        addLib("lib/bootstrap-modal.min.js");
        addLib("lib/bootstrap-modalmanager.min.js");
        addLib("lib/bootstrap.min.js");
        addLib("lib/bowser.min.js");
        addLib("lib/hopscotch.min.js");
        addLib("lib/lazy.min.js");
        addLib("lib/lodash.min.js");
        addLib("lib/prop-types.js");
        addLib("lib/qr-scanner.legacy.min.js");
        addLib("lib/react-jsonschema-form.js");
        addLib("lib/require.min.js");
        addLib("lib/Rx.min.js");
        addLib("lib/rxjs-operators.js");
        addLib("lib/rxjs-observable-concat.js");
        addLib("lib/trim-canvas.js");
        addLib("lib/tslib.js");
        addLib("lib/URI.js");

        // Prod versions of our React libs
        addLib("lib/react.production.min.js");
        addLib("lib/react-router-dom.min.js");
        addLib("lib/react-dom.production.min.js");
        addLib("lib/react-bootstrap.min.js");
        // Ensure dev versions are also in app-cache if in 'dev' mode - can we sort require-boot.js for this too?
        // These are purely the resources, its require-boot that chooses them.
        if (dev) {
            addLib("lib/react.development.js");
            addLib("lib/react-router-dom.js"); // Contains react-router module too
            addLib("lib/react-dom.development.js");
            addLib("lib/react-bootstrap.js");
        }

        addLib("lib/react-async/index.js");
        addLib("lib/react-addons-create-fragment.min.js");
        addLib("lib/react-transition-group.min.js");
        addLib("lib/select2/select2.min.js");

        addLib("jquery/jquery-3.6.0.min.js");
        addLib("jquery/jquery-migrate.min.js");
        addLib("jquery/jquery-bundle.js");
        addLib("jquery/jquery-ui.js"); // TODO MISSING, do we mean jquery-ui-bundle.js (or even ../jquery-ui.js)?
        addLib("jquery/jquery-datepicker.js");
        addLib("jquery/jquery-timepicker.min.js");
        addLib("jquery/jquery-ui-1.10.3.custom.min.js");
        addLib("jquery/jquery-ui-touch-punch.js");
        addLib("jquery-file-upload-9.5.8/jquery.fileupload.min.js");
        addLib("jquery-file-upload-9.5.8/jquery.iframe-transport.js");

        addLib("jquery-amd/typeahead.jquery.min.js");

        addLib("time/moment-init.js");
        addLib("time/moment.min.js");

        addLib("tests/qunit/qunit-1.11.0.js");

        addPng("themes/ecco/images/jquery/ui-bg_flat_55_fbec88_40x100.png");
        addPng("themes/ecco/images/jquery/ui-bg_glass_85_dfeffc_1x400.png");
        addPng("themes/ecco/images/jquery/ui-bg_glass_95_fef1ec_1x400.png");
        addPng("themes/ecco/images/jquery/ui-bg_gloss-wave_55_5c9ccc_500x100.png");
        addPng("themes/ecco/images/jquery/ui-bg_inset-hard_100_f5f8f9_1x100.png");
        addPng("themes/ecco/images/jquery/ui-bg_inset-hard_100_fcfdfd_1x100.png");
        addPng("themes/ecco/images/jquery/ui-bg_glass_75_d0e5f5_1x400.png");
        addPng("themes/ecco/images/jquery/ui-icons_217bc0_256x240.png");
        addPng("themes/ecco/images/jquery/ui-icons_2e83ff_256x240.png");
        addPng("themes/ecco/images/jquery/ui-icons_469bdd_256x240.png");
        addPng("themes/ecco/images/jquery/ui-icons_6da8d5_256x240.png");
        addPng("themes/ecco/images/jquery/ui-icons_cd0a0a_256x240.png");
        addPng("themes/ecco/images/jquery/ui-icons_d8e7f3_256x240.png");
        addPng("themes/ecco/images/datepicker.png");
        addPng("themes/ecco/images/ecco.png");
        addCachedResource("themes/ecco/images/favicon.ico", MediaType.APPLICATION_OCTET_STREAM);
        addPng("themes/ecco/images/flag_red24.png");
        addPng("themes/ecco/images/link.png");
        addCachedResource("themes/ecco/images/loading.gif", MediaType.APPLICATION_OCTET_STREAM);
        addPng("themes/ecco/images/logo_ecco.png");
        addPng("themes/ecco/images/logo_white.png");
        addPng("themes/ecco/images/logo_greyscale.png");
        addPng("themes/ecco/images/plus24.png");
        addPng("themes/ecco/images/star24.png");
        addPng("themes/ecco/images/networkstar24.png");
        addPng("themes/ecco/images/video_stack2.png");
    }

    private void addDistAndOptionallyDevScript(String path, boolean includeDev) {
        addScript("dist/" + path);
        if (includeDev) {
            addScript("debug/" + path);
        }
    }
}
