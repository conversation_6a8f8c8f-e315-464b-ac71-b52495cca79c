{"private": false, "name": "ecco-offline", "version": "0.0.0", "main": "build/offline/router.js", "types": "build/offline/router.d.ts", "scripts": {"clean": "tsc --build --clean scripts/tsconfig.json", "emit": "gulp emit && eslint --ext .ts,.tsx .", "build": "eslint --ext .ts,.tsx . && gulp emit", "gulp": "gulp", "copy-js": "gulp copy-js", "lint": "eslint --ext .ts,.tsx .", "test": "echo No gulp jest until fixed Jest 24 issues"}, "dependencies": {"@eccosolutions/ecco-common": "1.8.4", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui-controls": "0.0.0", "@softwareventures/array": "^3.2.0", "ecco-admin": "^0.0.0", "ecco-calendar": "^0.0.0", "ecco-commands": "^0.0.0", "ecco-components": "^0.0.0", "ecco-cubejs": "^0.0.0", "ecco-dto": "^0.0.0", "ecco-evidence": "^0.0.0", "ecco-finance": "^0.0.0", "ecco-incidents": "^0.0.0", "ecco-managedvoids": "^0.0.0", "ecco-repairs": "^0.0.0", "ecco-math": "^0.0.0", "ecco-offline-data": "^0.0.0", "ecco-rota": "^0.0.0", "json-patch-gen": "^1.0.2", "react-async": "10.0.1"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/preset-env": "^7.7.7", "@types/enzyme": "^3.1.14", "@types/gulp": "^4.0.6", "@types/jest": "^29.5.2", "@types/json-schema": "^7.0.11", "@types/lodash": "4.14.123", "@types/node": "^14.18.12", "@types/react": "^16.9.19", "@types/react-bootstrap": "^0.32.17", "@types/react-dom": "^16.9.5", "@types/react-router": "^5.1.5", "@types/react-router-dom": "^5.1.3", "@typescript-eslint/eslint-plugin": "^4.9.0", "@typescript-eslint/parser": "^4.9.0", "babel-jest": "^24.9.0", "bluebird": "^3.5.0", "del": "^3.0.0", "enzyme": "^3.6.0", "enzyme-adapter-react-16": "^1.5.0", "enzyme-to-json": "^3.3.4", "eslint": "^7.14.0", "glob": "^7.1.2", "gulp": "^4.0.0", "gulp-babel": "^8.0.0", "gulp-jest": "^4.0.2", "gulp-merge": "^0.1.1", "gulp-rename": "^1.2.2", "gulp-sourcemaps": "^2.6.4", "gulp-terser-js": "^5.2.2", "gulp-typescript": "https://github.com/eccosolutions/gulp-typescript.git", "gulp-util": "^3.0.8", "jest": "^29.5.0", "jest-cli": "^29.5.0", "jest-config": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "merge-stream": "^1.0.1", "moment": "2.24.0", "react": "16.13.1", "react-dom": "16.13.1", "rxjs": "^5.1.1", "ts-jest": "^29.1.0", "typescript": "5.1.5"}}