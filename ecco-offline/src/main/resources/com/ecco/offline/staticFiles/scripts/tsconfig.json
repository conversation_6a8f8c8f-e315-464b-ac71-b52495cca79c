{
    "extends": "./tsconfig-base.json",
    "compilerOptions": {
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        // These allow IDE to write directly to target folder - gulp will build to ../build (which IDE may copy)
        "declarationDir": "../build",
        "outDir": "../build",
        "sourceMap": true,
        "inlineSources": true,
        "module": "AMD",  // will need UMD for use with webpack
        "moduleResolution": "Node",
        "incremental": true, // not currently supported by gulp-typescript 5.0.0
        // The file below is in src and if we don't get output deleting it helps
        "tsBuildInfoFile": "./tsBuildInfo", // avoids things blowing up wiht tsc 3.4.3 via gulp-typescript for now
        "target": "ES6", // TODO: We should be able to go to 2018
        "noEmit": false,
        "baseUrl": "",
        "rootDir": ".", // Force this so tsc doesn't try to infer and gets it wrong when IDEA has done ../../ecco-ui/src/etc
        "types": ["@types/jest", "ecco-global"],
        "paths": {
            "ecco-admin": ["../../../../../../../../../ecco-ui/ecco-admin/index.ts"],
            "ecco-calendar": ["../../../../../../../../../ecco-ui/ecco-calendar/index.ts"],
            "ecco-components": ["../../../../../../../../../ecco-ui/ecco-components/index.ts"],
            "ecco-commands": ["../../../../../../../../../ecco-ui/ecco-commands/index.ts"],
            "ecco-cubejs": ["../../../../../../../../../ecco-ui/ecco-cubejs/index.ts"],
            "ecco-dto": ["../../../../../../../../../ecco-ui/ecco-dto/index.ts"],
            "ecco-evidence": ["../../../../../../../../../ecco-ui/ecco-evidence/index.ts"],
            "ecco-finance": ["../../../../../../../../../ecco-ui/ecco-finance/index.ts"],
            "ecco-forms": ["../../../../../../../../../ecco-ui/ecco-forms/index.ts"],
            "ecco-incidents": ["../../../../../../../../../ecco-ui/ecco-incidents/index.ts"],
            "ecco-managedvoids": ["../../../../../../../../../ecco-ui/ecco-managedvoids/index.ts"],
            "ecco-repairs": ["../../../../../../../../../ecco-ui/ecco-repairs/index.ts"],
            "ecco-math": ["../../../../../../../../../ecco-ui/ecco-math/index.ts"],
            "ecco-mui": ["../../../../../../../../../ecco-ui/ecco-mui/index.ts"],
            "ecco-offline-data": ["../../../../../../../../../ecco-ui/ecco-offline-data/index.ts"],
            "ecco-rota": ["../../../../../../../../../ecco-ui/ecco-rota/index.ts"],
            "*": ["*", "./typings/*"]
        },
        "lib": [
            "es2018",
            "dom",
        ],
        "importHelpers": true,
        "alwaysStrict": true,
        "jsx": "react",
        "strictNullChecks": false, // set true to continue DEV-715 WIP
        "strictPropertyInitialization": false // TODO: can switch to true when start using strictNullChecks
    },
    "references": [
        { "path": "../../../../../../../../../ecco-ui/ecco-admin/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-dto/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-calendar/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-commands/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-components/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-cubejs/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-evidence/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-finance/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-incidents/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-managedvoids/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-repairs/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-math/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-mui/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-offline-data/tsconfig.json" },
        { "path": "../../../../../../../../../ecco-ui/ecco-rota/tsconfig.json" }
    ],
    "exclude": ["__tests__"]
}
