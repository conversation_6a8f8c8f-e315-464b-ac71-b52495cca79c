{"private": true, "scripts": {"prepare": "husky install", "clean": "yarn workspaces run clean", "emit": "tsc --build --clean ecco-ui/tsconfig.json && tsc --build --verbose ecco-ui/tsconfig.json && yarn dev", "dev": "yarn nx run-many --target=emit --parallel --with-deps --projects=ecco-staff-app,ecco-test-app && yarn yarn workspace ecco-offline gulp copy-files", "build": "tsc --build --clean ecco-ui/tsconfig.json && tsc --build ecco-ui/tsconfig.json && yarn nx run-many --target=build --with-deps --projects=ecco-staff-app,ecco-test-app && yarn workspace ecco-offline gulp copy-files", "affected": "yarn nx affected --target=build --with-deps --projects=ecco-staff-app,ecco-test-app && yarn workspace ecco-offline gulp copy-files", "resume": "yarn nx run-many --only-failed --target=build --with-deps --projects=ecco-staff-app,ecco-test-app && yarn workspace ecco-offline gulp copy-files", "lint": "yarn nx run-many --target=lint --parallel --with-deps --projects=ecco-staff-app,ecco-test-app", "test": "yarn nx run-many --target=test --parallel --with-deps --projects=ecco-staff-app,ecco-ui-e2e,ecco-test-app", "gulp": "yarn workspace ecco-offline gulp", "dep-graph": "yarn nx dep-graph", "tsc": "tsc --build --clean ecco-ui/tsconfig.json && tsc --build ecco-ui/tsconfig.json && yarn workspace ecco-offline tsc --build --verbose scripts/tsconfig.json", "pre-commit": "precise-commits --whitelist=\"ecco-ui/**/*\""}, "workspaces": {"packages": ["ecco-ui/application-properties", "ecco-ui/ecco-admin", "ecco-ui/ecco-dto", "ecco-ui/ecco-commands", "ecco-ui/ecco-cubejs", "ecco-ui/ecco-offline-data", "ecco-ui/ecco-calendar", "ecco-ui/ecco-components", "ecco-ui/ecco-evidence", "ecco-ui/ecco-finance", "ecco-ui/ecco-forms", "ecco-ui/ecco-global", "ecco-ui/ecco-incidents", "ecco-ui/ecco-managedvoids", "ecco-ui/ecco-repairs", "ecco-ui/ecco-math", "ecco-ui/ecco-mui", "ecco-ui/ecco-mui-controls", "ecco-ui/ecco-rota", "ecco-ui/ecco-test-app", "ecco-offline/src/main/resources/com/ecco/offline/staticFiles", "ecco-ui/ecco-staff-app", "ecco-ui/_welcome_", "ecco-ui/ecco-ui-e2e"], "nohoist": []}, "dependencies": {}, "devDependencies": {"@nrwl/cli": "^12.10.0", "@nrwl/nx-cloud": "^12.3.14", "@nrwl/react": "^12.10.0", "@nrwl/tao": "^12.10.0", "@nrwl/workspace": "^12.10.0", "@softwareventures/precise-commits": "^2.0.8", "@softwareventures/prettier-config": "^2.0.0", "husky": "^7.0.4", "prettier": "^2.8.8", "typescript": "5.1.5", "yarn": "1.22.10"}, "prettier": "@softwareventures/prettier-config"}